#!/usr/bin/env python3
"""Process NFL depth charts into structured data."""

import pandas as pd
from pathlib import Path

# Depth chart data
depth_data = {
    "Miami Dolphins": {
        "QB": [("<PERSON><PERSON>", 1), ("<PERSON>", 2), ("<PERSON>", 3)],
        "<PERSON>": [("<PERSON><PERSON><PERSON>", 1), ("<PERSON><PERSON>", 2), ("<PERSON><PERSON> II", 3), ("<PERSON> Jr.", 4)],
        "WR": [("Tyreek Hill", 1), ("<PERSON><PERSON>", 1), ("<PERSON>", 1), ("<PERSON>", 2), ("<PERSON>", 2), ("<PERSON><PERSON><PERSON>", 2)],
        "TE": [("<PERSON>", 1), ("<PERSON>", 2), ("<PERSON>", 3), ("<PERSON><PERSON><PERSON>", 4)],
    },
    "Atlanta Falcons": {
        "QB": [("<PERSON>", 1), ("<PERSON>", 2), ("<PERSON><PERSON>", 3)],
        "RB": [("<PERSON><PERSON><PERSON>", 1), ("<PERSON>", 2), ("<PERSON> Jr.", 3), ("<PERSON>", 4)],
        "<PERSON>": [("<PERSON>", 1), ("<PERSON><PERSON>", 1), ("<PERSON>-<PERSON>d <PERSON>", 1), ("<PERSON><PERSON>", 2), ("<PERSON> <PERSON>", 2), ("<PERSON> <PERSON><PERSON><PERSON>", 2)],
        "<PERSON><PERSON>": [("<PERSON>.", 1), ("<PERSON> <PERSON><PERSON><PERSON>", 2), ("<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", 3), ("<PERSON><PERSON><PERSON><PERSON>", 4)],
    },
    "<PERSON> <PERSON>": {
        "<PERSON><PERSON>": [("<PERSON> Young", 1), ("Andy Dalton", 2)],
        "RB": [("Chuba Hubbard", 1), ("Rico Dowdle", 2), ("Trevor Etienne", 3), ("Jonathon Brooks", 4)],
        "WR": [("Tetairoa McMillan", 1), ("Xavier Legette", 1), ("Hunter Renfrow", 1), ("David Moore", 2), ("Brycen Tremayne", 2), ("Jimmy Horn Jr.", 2)],
        "TE": [("Ja'Tavion Sanders", 1), ("Tommy Tremble", 2), ("Mitchell Evans", 3), ("James Mitchell", 4)],
    },
    "Chicago Bears": {
        "QB": [("Caleb Williams", 1), ("Tyson Bagent", 2), ("Case Keenum", 3)],
        "RB": [("D'Andre Swift", 1), ("Roschon Johnson", 2), ("Kyle Monangai", 3), ("Travis Homer", 4)],
        "WR": [("DJ Moore", 1), ("Rome Odunze", 1), ("Luther Burden III", 1), ("Olamide Zaccheaus", 2), ("Devin Duvernay", 2), ("Jahdae Walker", 2)],
        "TE": [("Colston Loveland", 1), ("Cole Kmet", 2), ("Durham Smythe", 3)],
    },
    "Cincinnati Bengals": {
        "QB": [("Joe Burrow", 1), ("Jake Browning", 2)],
        "RB": [("Chase Brown", 1), ("Samaje Perine", 2), ("Tahj Brooks", 3)],
        "WR": [("Ja'Marr Chase", 1), ("Tee Higgins", 1), ("Andrei Iosivas", 1), ("Jermaine Burton", 2), ("Charlie Jones", 2), ("Mitchell Tinsley", 2)],
        "TE": [("Mike Gesicki", 1), ("Noah Fant", 2), ("Drew Sample", 3), ("Tanner Hudson", 4)],
    },
    "Denver Broncos": {
        "QB": [("Bo Nix", 1), ("Jarrett Stidham", 2), ("Sam Ehlinger", 3)],
        "RB": [("J.K. Dobbins", 1), ("RJ Harvey", 2), ("Tyler Badie", 3), ("Jaleel McLaughlin", 4)],
        "WR": [("Courtland Sutton", 1), ("Marvin Mims Jr.", 1), ("Troy Franklin", 1), ("Pat Bryant", 2), ("Trent Sherfield Sr.", 2)],
        "TE": [("Evan Engram", 1), ("Adam Trautman", 2), ("Nate Adkins", 3), ("Lucas Krull", 4)],
    },
    "Detroit Lions": {
        "QB": [("Jared Goff", 1), ("Kyle Allen", 2)],
        "RB": [("Jahmyr Gibbs", 1), ("David Montgomery", 2), ("Craig Reynolds", 3), ("Sione Vaki", 4)],
        "WR": [("Amon-Ra St. Brown", 1), ("Jameson Williams", 1), ("Kalif Raymond", 1), ("Isaac TeSlaa", 2), ("Dominic Lovett", 2)],
        "TE": [("Sam LaPorta", 1), ("Brock Wright", 2), ("Shane Zylstra", 3), ("Kenny Yeboah", 4)],
    },
    "Green Bay Packers": {
        "QB": [("Jordan Love", 1), ("Malik Willis", 2)],
        "RB": [("Josh Jacobs", 1), ("Emanuel Wilson", 2), ("Chris Brooks", 3), ("MarShawn Lloyd", 4)],
        "WR": [("Jayden Reed", 1), ("Matthew Golden", 1), ("Romeo Doubs", 1), ("Dontayvion Wicks", 2), ("Christian Watson", 2), ("Savion Williams", 2)],
        "TE": [("Tucker Kraft", 1), ("Luke Musgrave", 2), ("John FitzPatrick", 3), ("Ben Sims", 4)],
    },
    "Houston Texans": {
        "QB": [("C.J. Stroud", 1), ("Davis Mills", 2), ("Graham Mertz", 3)],
        "RB": [("Nick Chubb", 1), ("Dameon Pierce", 2), ("Dare Ogunbowale", 3), ("Woody Marks", 4)],
        "WR": [("Nico Collins", 1), ("Christian Kirk", 1), ("Jayden Higgins", 1), ("Jaylin Noel", 2), ("Xavier Hutchinson", 2), ("Justin Watson", 2)],
        "TE": [("Dalton Schultz", 1), ("Cade Stover", 2), ("Brevin Jordan", 3), ("Irv Smith Jr.", 4)],
    },
    "Indianapolis Colts": {
        "QB": [("Daniel Jones", 1), ("Anthony Richardson Sr.", 2), ("Riley Leonard", 3)],
        "RB": [("Jonathan Taylor", 1), ("Tyler Goodson", 2), ("DJ Giddens", 3), ("Ulysses Bentley IV", 4)],
        "WR": [("Michael Pittman Jr.", 1), ("Josh Downs", 1), ("Alec Pierce", 1), ("Adonai Mitchell", 2), ("Ashton Dulin", 2), ("Anthony Gould", 2)],
        "TE": [("Tyler Warren", 1), ("Mo Alie-Cox", 2), ("Drew Ogletree", 3), ("Will Mallory", 4)],
    },
    "Jacksonville Jaguars": {
        "QB": [("Trevor Lawrence", 1), ("Nick Mullens", 2)],
        "RB": [("Travis Etienne Jr.", 1), ("Tank Bigsby", 2), ("Bhayshul Tuten", 3), ("LeQuint Allen Jr.", 4)],
        "WR": [("Brian Thomas Jr.", 1), ("Travis Hunter", 1), ("Dyami Brown", 1), ("Parker Washington", 2), ("Tim Patrick", 2), ("Joshua Cephus", 2)],
        "TE": [("Brenton Strange", 1), ("Johnny Mundt", 2), ("Hunter Long", 3)],
    },
    "Las Vegas Raiders": {
        "QB": [("Geno Smith", 1), ("Kenny Pickett", 2), ("Aidan O'Connell", 3)],
        "RB": [("Ashton Jeanty", 1), ("Zamir White", 2), ("Dylan Laube", 3), ("Raheem Mostert", 4)],
        "WR": [("Jakobi Meyers", 1), ("Tre Tucker", 1), ("Dont'e Thornton Jr.", 1), ("Jack Bech", 2), ("Justin Shorter", 2), ("Alex Bachman", 2)],
        "TE": [("Brock Bowers", 1), ("Michael Mayer", 2), ("Ian Thomas", 3)],
    },
    "Los Angeles Rams": {
        "QB": [("Matthew Stafford", 1), ("Jimmy Garoppolo", 2), ("Stetson Bennett IV", 3)],
        "RB": [("Kyren Williams", 1), ("Blake Corum", 2), ("Jarquez Hunter", 3), ("Cody Schrader", 4)],
        "WR": [("Puka Nacua", 1), ("Davante Adams", 1), ("Tutu Atwell", 1), ("Jordan Whittington", 2), ("Konata Mumpfield", 2), ("Xavier Smith", 2)],
        "TE": [("Tyler Higbee", 1), ("Terrance Ferguson", 2), ("Colby Parkinson", 3), ("Davis Allen", 4)],
    },
    "Minnesota Vikings": {
        "QB": [("J.J. McCarthy", 1), ("Carson Wentz", 2), ("Max Brosmer", 3)],
        "RB": [("Aaron Jones Sr.", 1), ("Jordan Mason", 2), ("Ty Chandler", 3), ("Zavier Scott", 4)],
        "WR": [("Justin Jefferson", 1), ("Adam Thielen", 1), ("Jalen Nailor", 1), ("Tai Felton", 2), ("Myles Price", 2), ("Rondale Moore", 2)],
        "TE": [("T.J. Hockenson", 1), ("Josh Oliver", 2), ("Gavin Bartholomew", 3)],
    },
    "New England Patriots": {
        "QB": [("Drake Maye", 1), ("Joshua Dobbs", 2), ("Tommy DeVito", 3)],
        "RB": [("Rhamondre Stevenson", 1), ("TreVeyon Henderson", 2), ("Antonio Gibson", 3), ("Lan Larison", 4)],
        "WR": [("Stefon Diggs", 1), ("DeMario Douglas", 1), ("Kayshon Boutte", 1), ("Mack Hollins", 2), ("Kyle Williams", 2), ("Ja'Lynn Polk", 2)],
        "TE": [("Hunter Henry", 1), ("Austin Hooper", 2)],
    },
    "New Orleans Saints": {
        "QB": [("Spencer Rattler", 1), ("Tyler Shough", 2)],
        "RB": [("Alvin Kamara", 1), ("Kendre Miller", 2), ("Devin Neal", 3), ("Velus Jones Jr.", 4)],
        "WR": [("Chris Olave", 1), ("Rashid Shaheed", 1), ("Brandin Cooks", 1), ("Devaughn Vele", 2), ("Trey Palmer", 2), ("Mason Tipton", 2)],
        "TE": [("Juwan Johnson", 1), ("Moliki Matavao", 2), ("Jack Stoll", 3), ("Taysom Hill", 4)],
    },
    "New York Jets": {
        "QB": [("Justin Fields", 1), ("Tyrod Taylor", 2)],
        "RB": [("Breece Hall", 1), ("Braelon Allen", 2), ("Isaiah Davis", 3), ("Kene Nwangwu", 4)],
        "WR": [("Garrett Wilson", 1), ("Josh Reynolds", 1), ("Tyler Johnson", 1), ("Xavier Gipson", 2), ("Arian Smith", 2), ("Allen Lazard", 2)],
        "TE": [("Mason Taylor", 1), ("Jeremy Ruckert", 2), ("Stone Smartt", 3), ("Jelani Woods", 4)],
    },
    "Pittsburgh Steelers": {
        "QB": [("Aaron Rodgers", 1), ("Mason Rudolph", 2), ("Skylar Thompson", 3), ("Will Howard", 4)],
        "RB": [("Jaylen Warren", 1), ("Kaleb Johnson", 2), ("Kenneth Gainwell", 3)],
        "WR": [("DK Metcalf", 1), ("Calvin Austin III", 1), ("Roman Wilson", 1), ("Scotty Miller", 2), ("Ben Skowronek", 2)],
        "TE": [("Pat Freiermuth", 1), ("Jonnu Smith", 2), ("Darnell Washington", 3), ("Donald Parham Jr.", 4)],
    },
    "San Francisco 49ers": {
        "QB": [("Brock Purdy", 1), ("Mac Jones", 2), ("Kurtis Rourke", 3)],
        "RB": [("Christian McCaffrey", 1), ("Brian Robinson Jr.", 2), ("Isaac Guerendo", 3), ("Jordan James", 4)],
        "WR": [("Jauan Jennings", 1), ("Ricky Pearsall", 1), ("Marquez Valdes-Scantling", 1), ("Skyy Moore", 2), ("Jordan Watkins", 2), ("Russell Gage Jr.", 2)],
        "TE": [("George Kittle", 1), ("Luke Farrell", 2), ("Jake Tonges", 3)],
    },
    "Seattle Seahawks": {
        "QB": [("Sam Darnold", 1), ("Drew Lock", 2), ("Jalen Milroe", 3)],
        "RB": [("Kenneth Walker III", 1), ("Zach Charbonnet", 2), ("George Holani", 3), ("Kenny McIntosh", 4)],
        "WR": [("Jaxon Smith-Njigba", 1), ("Cooper Kupp", 1), ("Tory Horton", 1), ("Jake Bobo", 2), ("Dareke Young", 2), ("Cody White", 2)],
        "TE": [("AJ Barner", 1), ("Elijah Arroyo", 2), ("Eric Saubert", 3), ("Nick Kallerup", 4)],
    },
    "Tennessee Titans": {
        "QB": [("Cam Ward", 1), ("Brandon Allen", 2), ("Will Levis", 3)],
        "RB": [("Tony Pollard", 1), ("Julius Chestnut", 2), ("Kalel Mullings", 3), ("Tyjae Spears", 4)],
        "WR": [("Calvin Ridley", 1), ("Tyler Lockett", 1), ("Elic Ayomanor", 1), ("Van Jefferson", 2), ("Chimere Dike", 2), ("Bryce Oliver", 2)],
        "TE": [("Chig Okonkwo", 1), ("Gunnar Helm", 2), ("David Martin-Robinson", 3)],
    },
    "Washington Commanders": {
        "QB": [("Jayden Daniels", 1), ("Marcus Mariota", 2), ("Josh Johnson", 3)],
        "RB": [("Austin Ekeler", 1), ("Jacory Croskey-Merritt", 2), ("Jeremy McNichols", 3), ("Chris Rodriguez Jr.", 4)],
        "WR": [("Terry McLaurin", 1), ("Deebo Samuel", 1), ("Noah Brown", 1), ("Luke McCaffrey", 2), ("Jaylin Lane", 2), ("Chris Moore", 2)],
        "TE": [("Zach Ertz", 1), ("John Bates", 2), ("Ben Sinnott", 3), ("Colson Yankoff", 4)],
    },
    "Tampa Bay Buccaneers": {
        "QB": [("Baker Mayfield", 1), ("Teddy Bridgewater", 2)],
        "RB": [("Bucky Irving", 1), ("Rachaad White", 2), ("Sean Tucker", 3), ("Josh Williams", 4)],
        "WR": [("Mike Evans", 1), ("Chris Godwin Jr.", 1), ("Emeka Egbuka", 1), ("Tez Johnson", 2), ("Sterling Shepard", 2), ("Ryan Miller", 2)],
        "TE": [("Cade Otton", 1), ("Payne Durham", 2), ("Ko Kieft", 3), ("Devin Culp", 4)],
    },
    "Arizona Cardinals": {
        "QB": [("Kyler Murray", 1), ("Jacoby Brissett", 2)],
        "RB": [("James Conner", 1), ("Trey Benson", 2), ("Emari Demercado", 3), ("Bam Knight", 4)],
        "WR": [("Marvin Harrison Jr.", 1), ("Michael Wilson", 1), ("Zay Jones", 1), ("Greg Dortch", 2), ("Xavier Weaver", 2), ("Trishton Jackson", 2)],
        "TE": [("Trey McBride", 1), ("Elijah Higgins", 2), ("Tip Reiman", 3), ("Travis Vokolek", 4)],
    },
    "New York Giants": {
        "QB": [("Russell Wilson", 1), ("Jaxson Dart", 2), ("Jameis Winston", 3)],
        "RB": [("Tyrone Tracy Jr.", 1), ("Devin Singletary", 2), ("Cam Skattebo", 3), ("Eric Gray", 4)],
        "WR": [("Malik Nabers", 1), ("Wan'Dale Robinson", 1), ("Darius Slayton", 1), ("Jalin Hyatt", 2), ("Beaux Collins", 2), ("Gunner Olszewski", 2)],
        "TE": [("Theo Johnson", 1), ("Daniel Bellinger", 2), ("Chris Manhertz", 3), ("Thomas Fidone II", 4)],
    },
    "Cleveland Browns": {
        "QB": [("Joe Flacco", 1), ("Dillon Gabriel", 2), ("Shedeur Sanders", 3), ("Deshaun Watson", 4)],
        "RB": [("Jerome Ford", 1), ("Dylan Sampson", 2), ("Quinshon Judkins", 3), ("Raheim Sanders", 4)],
        "WR": [("Jerry Jeudy", 1), ("Cedric Tillman", 1), ("Jamari Thrash", 1), ("Isaiah Bond", 2), ("DeAndre Carter", 2), ("Gage Larvadain", 2)],
        "TE": [("David Njoku", 1), ("Harold Fannin Jr.", 2), ("Blake Whiteheart", 3)],
    },
}

# Convert to DataFrame format
rows = []
for team, positions in depth_data.items():
    for position, players in positions.items():
        for player_name, depth in players:
            # Clean player name (remove status indicators)
            clean_name = player_name.replace(" O", "").replace(" IR", "").replace(" Q", "").replace(" SUSP", "").replace(" D", "").strip()
            rows.append({
                'team': team,
                'player_name': clean_name,
                'position': position,
                'depth_chart_order': depth,
                'is_starter': depth == 1
            })

df = pd.DataFrame(rows)
print(f"Created depth chart data with {len(df)} entries")
print(f"Teams: {df['team'].nunique()}")
print(f"Positions: {df['position'].unique()}")

# Save to parquet
output_path = Path("data/depth_week1.parquet")
output_path.parent.mkdir(parents=True, exist_ok=True)
df.to_parquet(output_path, index=False)
print(f"Saved depth chart data to: {output_path}")

print("\nFirst 10 rows:")
print(df.head(10).to_string())
