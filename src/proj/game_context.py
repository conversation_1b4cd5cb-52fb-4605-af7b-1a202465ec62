#!/usr/bin/env python3
"""
Game context integration for NFL projections.
Integrates team totals, spreads, weather, and other contextual factors.
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path


class GameContextEngine:
    """Integrates game context data into player projections."""
    
    def __init__(self):
        self.odds_data = None
        self.weather_data = None
        self.game_contexts = {}
        
    def load_odds_data(self, odds_file: str = "data/odds_week1.json"):
        """Load odds data from JSON file."""
        with open(odds_file, 'r') as f:
            self.odds_data = json.load(f)
        print(f"Loaded {len(self.odds_data)} games from odds data")
        
    def load_weather_data(self, weather_file: str = "data/weather_week1.parquet"):
        """Load weather data from parquet file."""
        if Path(weather_file).exists():
            self.weather_data = pd.read_parquet(weather_file)
            print(f"Loaded weather data for {len(self.weather_data)} games")
        else:
            print(f"Weather file {weather_file} not found")
            
    def extract_game_context(self, team1: str, team2: str) -> Dict[str, Any]:
        """Extract game context for a specific matchup."""
        if not self.odds_data:
            raise ValueError("Odds data not loaded. Call load_odds_data() first.")

        # Map team abbreviations to full names if needed
        team_name_map = {
            'TEN': 'Tennessee Titans',
            'DEN': 'Denver Broncos',
            'SF': 'San Francisco 49ers',
            'SEA': 'Seattle Seahawks',
            'DET': 'Detroit Lions',
            'GB': 'Green Bay Packers',
            'HOU': 'Houston Texans',
            'LAR': 'Los Angeles Rams'
        }

        # Convert to full names if they're abbreviations
        full_team1 = team_name_map.get(team1, team1)
        full_team2 = team_name_map.get(team2, team2)

        # Find the game in odds data
        game_data = None
        for game in self.odds_data:
            if ((game['home_team'] == full_team1 and game['away_team'] == full_team2) or
                (game['home_team'] == full_team2 and game['away_team'] == full_team1)):
                game_data = game
                break
                
        if not game_data:
            print(f"Warning: No odds data found for {team1} vs {team2}")
            return {}
            
        # Extract DraftKings odds
        dk_odds = None
        for bookmaker in game_data['bookmakers']:
            if bookmaker['key'] == 'draftkings':
                dk_odds = bookmaker
                break
                
        if not dk_odds:
            print(f"Warning: No DraftKings odds found for {team1} vs {team2}")
            return {}
            
        # Parse spreads and totals
        spread_data = None
        total_data = None
        
        for market in dk_odds['markets']:
            if market['key'] == 'spreads':
                spread_data = market['outcomes']
            elif market['key'] == 'totals':
                total_data = market['outcomes']
                
        context = {
            'home_team': game_data['home_team'],
            'away_team': game_data['away_team'],
            'commence_time': game_data['commence_time']
        }
        
        if spread_data and total_data:
            # Get spread and total
            home_spread = next((o['point'] for o in spread_data if o['name'] == game_data['home_team']), 0)
            away_spread = next((o['point'] for o in spread_data if o['name'] == game_data['away_team']), 0)
            total = total_data[0]['point'] if total_data else 45.0
            
            # Calculate implied team totals
            home_total = (total - home_spread) / 2
            away_total = (total + home_spread) / 2
            
            context.update({
                'total': total,
                'home_spread': home_spread,
                'away_spread': away_spread,
                'home_total': home_total,
                'away_total': away_total,
                'spread_magnitude': abs(home_spread),
                'is_high_total': total > 47.0,
                'is_low_total': total < 42.0,
                'game_script': self._determine_game_script(home_spread, total)
            })
            
        # Add weather context if available
        if self.weather_data is not None:
            weather_context = self._get_weather_context(game_data['home_team'], game_data['away_team'])
            context.update(weather_context)
            
        return context
        
    def _determine_game_script(self, spread: float, total: float) -> str:
        """Determine likely game script based on spread and total."""
        spread_mag = abs(spread)
        
        if spread_mag >= 7.0:
            return "blowout_likely"
        elif spread_mag >= 3.5:
            return "moderate_favorite"
        elif total >= 47.0:
            return "high_scoring"
        elif total <= 42.0:
            return "low_scoring"
        else:
            return "competitive"
            
    def _get_weather_context(self, home_team: str, away_team: str) -> Dict[str, Any]:
        """Get weather context for the game."""
        # This would integrate with weather data
        # For now, return empty dict
        return {}
        
    def apply_game_context_to_projections(self, projections_df: pd.DataFrame) -> pd.DataFrame:
        """Apply game context adjustments to player projections."""
        adjusted_df = projections_df.copy()
        
        # Group by team to apply team-level context
        for team in adjusted_df['team'].unique():
            team_players = adjusted_df[adjusted_df['team'] == team]
            
            # Find opponent
            opponent = self._find_opponent(team, adjusted_df)
            if not opponent:
                continue
                
            # Get game context
            context = self.extract_game_context(team, opponent)
            if not context:
                continue
                
            print(f"\nApplying context for {team} vs {opponent}:")
            print(f"  Total: {context.get('total', 'N/A')}")
            print(f"  {team} implied total: {context.get('home_total' if context.get('home_team') == team else 'away_total', 'N/A'):.1f}")
            print(f"  Spread: {context.get('home_spread' if context.get('home_team') == team else 'away_spread', 'N/A')}")
            print(f"  Game script: {context.get('game_script', 'N/A')}")
            
            # Apply adjustments
            team_mask = adjusted_df['team'] == team
            adjusted_df.loc[team_mask] = self._apply_team_context_adjustments(
                adjusted_df.loc[team_mask], context, team
            )
            
        return adjusted_df
        
    def _find_opponent(self, team: str, projections_df: pd.DataFrame) -> Optional[str]:
        """Find the opponent for a given team."""
        # Map abbreviations to full team names for odds lookup
        team_name_map = {
            'TEN': 'Tennessee Titans',
            'DEN': 'Denver Broncos',
            'SF': 'San Francisco 49ers',
            'SEA': 'Seattle Seahawks',
            'DET': 'Detroit Lions',
            'GB': 'Green Bay Packers',
            'HOU': 'Houston Texans',
            'LAR': 'Los Angeles Rams'
        }

        # Opponent mappings
        opponent_map = {
            'TEN': 'DEN', 'DEN': 'TEN',
            'SF': 'SEA', 'SEA': 'SF',
            'DET': 'GB', 'GB': 'DET',
            'HOU': 'LAR', 'LAR': 'HOU'
        }

        opponent_abbrev = opponent_map.get(team)
        if opponent_abbrev:
            return team_name_map.get(opponent_abbrev)
        return None
        
    def _apply_team_context_adjustments(self, team_df: pd.DataFrame, context: Dict[str, Any], team: str) -> pd.DataFrame:
        """Apply context-based adjustments to team projections."""
        adjusted_df = team_df.copy()
        
        # Get team's implied total
        is_home = context.get('home_team') == team
        team_total = context.get('home_total' if is_home else 'away_total', 22.0)
        team_spread = context.get('home_spread' if is_home else 'away_spread', 0.0)
        game_script = context.get('game_script', 'competitive')
        total = context.get('total', 45.0)
        
        # Apply position-specific adjustments
        for idx, player in adjusted_df.iterrows():
            position = player['position']
            current_proj = player['proj_mean']
            
            # Base team total scaling
            team_total_multiplier = self._get_team_total_multiplier(team_total, position)
            
            # Game script adjustments
            script_multiplier = self._get_game_script_multiplier(game_script, team_spread, position)
            
            # Weather adjustments (if available)
            weather_multiplier = self._get_weather_multiplier(context, position)
            
            # Combined adjustment
            total_multiplier = team_total_multiplier * script_multiplier * weather_multiplier
            
            # Apply adjustment
            new_proj = current_proj * total_multiplier
            adjusted_df.at[idx, 'proj_mean'] = new_proj
            
            # Adjust percentiles proportionally
            for pct in ['p50', 'p75', 'p90']:
                if pct in adjusted_df.columns:
                    adjusted_df.at[idx, pct] = player[pct] * total_multiplier
                    
            # Update confidence based on context availability
            context_confidence = self._calculate_context_confidence(context)
            current_confidence = player.get('confidence', 0.0)
            adjusted_df.at[idx, 'confidence'] = min(current_confidence + context_confidence, 1.0)
            
        return adjusted_df
        
    def _get_team_total_multiplier(self, team_total: float, position: str) -> float:
        """Get multiplier based on team's implied total."""
        # League average team total is ~22.5 points
        league_avg = 22.5
        base_multiplier = team_total / league_avg
        
        # Position-specific sensitivity to team total
        position_sensitivity = {
            'QB': 1.0,    # QBs scale directly with team total
            'RB': 0.8,    # RBs less sensitive (game script matters more)
            'WR': 1.1,    # WRs very sensitive to team total
            'TE': 0.9     # TEs moderately sensitive
        }
        
        sensitivity = position_sensitivity.get(position, 1.0)
        
        # Apply sensitivity (1.0 = no change, >1.0 = more sensitive)
        return 1.0 + (base_multiplier - 1.0) * sensitivity
        
    def _get_game_script_multiplier(self, game_script: str, spread: float, position: str) -> float:
        """Get multiplier based on expected game script."""
        multipliers = {
            'QB': {
                'blowout_likely': 0.95 if spread > 0 else 1.1,  # Losing team throws more
                'moderate_favorite': 1.02 if spread > 0 else 1.05,
                'high_scoring': 1.08,
                'low_scoring': 0.92,
                'competitive': 1.0
            },
            'RB': {
                'blowout_likely': 1.1 if spread > 0 else 0.85,   # Winning team runs more
                'moderate_favorite': 1.05 if spread > 0 else 0.95,
                'high_scoring': 0.95,  # Less running in shootouts
                'low_scoring': 1.05,   # More running in low-scoring games
                'competitive': 1.0
            },
            'WR': {
                'blowout_likely': 0.9 if spread > 0 else 1.15,   # Losing team throws more
                'moderate_favorite': 1.0,
                'high_scoring': 1.1,
                'low_scoring': 0.9,
                'competitive': 1.0
            },
            'TE': {
                'blowout_likely': 0.95 if spread > 0 else 1.05,
                'moderate_favorite': 1.0,
                'high_scoring': 1.05,
                'low_scoring': 0.95,
                'competitive': 1.0
            }
        }
        
        return multipliers.get(position, {}).get(game_script, 1.0)
        
    def _get_weather_multiplier(self, context: Dict[str, Any], position: str) -> float:
        """Get multiplier based on weather conditions."""
        # Placeholder for weather adjustments
        return 1.0
        
    def _calculate_context_confidence(self, context: Dict[str, Any]) -> float:
        """Calculate confidence boost from available context."""
        confidence_boost = 0.0
        
        if 'total' in context:
            confidence_boost += 0.2
        if 'home_spread' in context:
            confidence_boost += 0.2
        if 'game_script' in context:
            confidence_boost += 0.1
            
        return confidence_boost


def main():
    """Test the game context engine."""
    engine = GameContextEngine()
    engine.load_odds_data()
    
    # Test with target games
    test_games = [
        ('TEN', 'DEN'),
        ('SF', 'SEA'),
        ('DET', 'GB'),
        ('HOU', 'LAR')
    ]
    
    for team1, team2 in test_games:
        context = engine.extract_game_context(team1, team2)
        print(f"\n{team1} vs {team2}:")
        for key, value in context.items():
            print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
