#!/usr/bin/env python3
"""Create focused projections for specific games using full game context."""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Team abbreviation mappings
TEAM_MAPPINGS = {
    'Tennessee Titans': 'TEN',
    'Denver Broncos': 'DEN', 
    'San Francisco 49ers': 'SF',
    'Seattle Seahawks': 'SEA',
    'Detroit Lions': 'DET',
    'Green Bay Packers': 'GB',
    'Houston Texans': 'HOU',
    'Los Angeles Rams': 'LAR'
}

# Target games
TARGET_GAMES = [
    ('TEN', 'DEN'),  # Tennessee vs Denver
    ('SF', 'SEA'),   # SF vs SEA  
    ('DET', 'GB'),   # DET vs GB
    ('HOU', 'LAR')   # Houston vs LAR
]

TARGET_TEAMS = ['TEN', 'DEN', 'SF', 'SEA', 'DET', 'GB', 'HOU', 'LAR']

def load_depth_charts() -> pd.DataFrame:
    """Load depth chart data."""
    return pd.read_parquet('data/depth_week1.parquet')

def load_player_props() -> pd.DataFrame:
    """Load player props data."""
    return pd.read_parquet('data/player_props.parquet')

def load_defensive_mappings() -> Dict[str, str]:
    """Load defensive player to team mappings from create_team_defense_projections.py"""
    # Import the mapping directly
    exec(open('create_team_defense_projections.py').read(), globals())
    return def_player_teams

def create_team_name_mapping() -> Dict[str, str]:
    """Create mapping from full team names to abbreviations."""
    depth_df = load_depth_charts()
    team_mapping = {}
    
    for full_name, abbrev in TEAM_MAPPINGS.items():
        # Find teams in depth chart that match
        matching_teams = depth_df[depth_df['team'].str.contains(abbrev.replace('SF', 'San Francisco').replace('GB', 'Green Bay'), case=False, na=False)]['team'].unique()
        for team in matching_teams:
            team_mapping[team] = abbrev
            
    # Direct mappings for exact matches
    team_mapping.update({
        'Tennessee Titans': 'TEN',
        'Denver Broncos': 'DEN',
        'San Francisco 49ers': 'SF', 
        'Seattle Seahawks': 'SEA',
        'Detroit Lions': 'DET',
        'Green Bay Packers': 'GB',
        'Houston Texans': 'HOU',
        'Los Angeles Rams': 'LAR'
    })
    
    return team_mapping

def get_game_context(team1: str, team2: str) -> Dict[str, Any]:
    """Get game context including totals and spreads."""
    # Load odds data if available
    odds_file = Path('data/odds_week1.json')
    if odds_file.exists():
        with open(odds_file, 'r') as f:
            odds_data = json.load(f)
    else:
        odds_data = {}
    
    # Default game context
    context = {
        'total': 45.0,
        'spread': 0.0,
        'team1_total': 22.5,
        'team2_total': 22.5,
        'game_script': 'competitive'
    }
    
    # Game-specific contexts based on typical matchups
    game_contexts = {
        ('TEN', 'DEN'): {'total': 42.5, 'spread': -3.0, 'team1_total': 19.75, 'team2_total': 22.75},
        ('SF', 'SEA'): {'total': 47.5, 'spread': -2.5, 'team1_total': 25.0, 'team2_total': 22.5},
        ('DET', 'GB'): {'total': 51.5, 'spread': -1.5, 'team1_total': 26.5, 'team2_total': 25.0},
        ('HOU', 'LAR'): {'total': 49.0, 'spread': -4.0, 'team1_total': 26.5, 'team2_total': 22.5}
    }
    
    game_key = (team1, team2)
    if game_key in game_contexts:
        context.update(game_contexts[game_key])
    elif (team2, team1) in game_contexts:
        # Flip the context
        flipped = game_contexts[(team2, team1)]
        context.update({
            'total': flipped['total'],
            'spread': -flipped['spread'],
            'team1_total': flipped['team2_total'],
            'team2_total': flipped['team1_total']
        })
    
    return context

def create_offensive_projections(depth_df: pd.DataFrame, props_df: pd.DataFrame, 
                                team_mapping: Dict[str, str]) -> pd.DataFrame:
    """Create projections for offensive players using depth charts and props."""
    
    # Map team names to abbreviations
    depth_df = depth_df.copy()
    depth_df['team_abbrev'] = depth_df['team'].map(team_mapping)
    
    # Filter for target teams
    target_depth = depth_df[depth_df['team_abbrev'].isin(TARGET_TEAMS)].copy()
    
    projections = []
    
    for _, player in target_depth.iterrows():
        player_name = player['player_name']
        team = player['team_abbrev']
        position = player['position']
        depth_order = player['depth_chart_order']
        is_starter = player['is_starter']
        
        # Find player props
        player_props = props_df[props_df['player_name'] == player_name]
        
        # Base projection based on position and depth
        base_projections = {
            'QB': {1: 18.0, 2: 8.0, 3: 4.0},
            'RB': {1: 12.0, 2: 6.0, 3: 3.0, 4: 1.5},
            'WR': {1: 11.0, 2: 7.0, 3: 4.0, 4: 2.0},
            'TE': {1: 8.0, 2: 4.0, 3: 2.0, 4: 1.0}
        }
        
        base_proj = base_projections.get(position, {}).get(depth_order, 2.0)
        
        # Adjust based on player props if available
        final_proj = base_proj
        if len(player_props) > 0:
            # Use props to adjust projection
            prop_adjustment = 1.0
            
            # Key markets for adjustment
            key_markets = ['pass_yards', 'rush_yards', 'rec_yards', 'receptions', 'pass_tds']
            relevant_props = player_props[player_props['market'].isin(key_markets)]
            
            if len(relevant_props) > 0:
                # Scale based on prop lines
                avg_line = relevant_props['line'].mean()
                if position == 'QB' and avg_line > 0:
                    prop_adjustment = min(2.0, max(0.5, avg_line / 250.0))
                elif position == 'RB' and avg_line > 0:
                    prop_adjustment = min(2.0, max(0.5, avg_line / 80.0))
                elif position in ['WR', 'TE'] and avg_line > 0:
                    prop_adjustment = min(2.0, max(0.5, avg_line / 60.0))
            
            final_proj *= prop_adjustment
        
        projections.append({
            'Player': player_name,
            'Projection': round(final_proj, 2),
            'team': team,
            'position': position,
            'depth_order': depth_order,
            'is_starter': is_starter
        })
    
    return pd.DataFrame(projections)

def create_team_defense_projections_focused() -> pd.DataFrame:
    """Create team defense projections for target teams only using correct DraftKings scoring."""

    # Load defensive mappings
    def_mappings = load_defensive_mappings()
    props_df = load_player_props()

    # Filter for defensive markets (only sacks and interceptions score points)
    def_props = props_df[props_df['market'].isin(['sacks', 'interceptions'])].copy()
    def_props['team'] = def_props['player_name'].map(def_mappings)
    def_props = def_props.dropna(subset=['team'])
    def_props = def_props[def_props['team'].isin(TARGET_TEAMS)]

    team_def_projections = []

    for team in TARGET_TEAMS:
        team_players = def_props[def_props['team'] == team]

        # Calculate team sacks projection (1 point each)
        sacks_players = team_players[team_players['market'] == 'sacks']
        team_sacks = sacks_players['line'].sum() if len(sacks_players) > 0 else 1.0

        # Calculate team interceptions projection (2 points each)
        int_players = team_players[team_players['market'] == 'interceptions']
        team_ints = int_players['line'].sum() if len(int_players) > 0 else 0.8

        # DraftKings Defense Scoring:
        # Sacks: 1 pt each
        # Interceptions: 2 pts each
        # Points Allowed: varies by tier (we'll estimate based on team strength)
        # Fumble recoveries, TDs, etc. (harder to project, use team strength)

        sack_points = team_sacks * 1.0  # 1 pt per sack
        int_points = team_ints * 2.0    # 2 pts per interception

        # Estimate points allowed tier based on team defense strength
        points_allowed_score = {
            'SF': 6.0,   # Strong defense, likely 1-6 points allowed (+7) or 7-13 (+4)
            'DET': 5.0,  # Good defense
            'SEA': 5.0,  # Good defense
            'GB': 3.0,   # Average defense, likely 14-20 points allowed (+1)
            'LAR': 2.0,  # Below average
            'HOU': 2.0,  # Below average
            'DEN': 1.0,  # Weak defense, likely 21+ points allowed (0 to -1)
            'TEN': 0.0   # Weak defense
        }.get(team, 1.0)

        # Add estimated points for fumble recoveries, blocked kicks, etc.
        misc_points = {
            'SF': 2.0, 'DET': 1.5, 'SEA': 1.5, 'GB': 1.0,
            'LAR': 1.0, 'HOU': 1.0, 'DEN': 0.5, 'TEN': 0.5
        }.get(team, 1.0)

        team_def_score = sack_points + int_points + points_allowed_score + misc_points

        team_def_projections.append({
            'Player': f"{team} Defense",
            'Projection': round(team_def_score, 2),
            'team': team,
            'position': 'DEF'
        })

    return pd.DataFrame(team_def_projections)

def apply_game_context_adjustments(projections_df: pd.DataFrame) -> pd.DataFrame:
    """Apply game context adjustments to projections."""
    
    adjusted_df = projections_df.copy()
    
    # Apply game-by-game adjustments
    for team1, team2 in TARGET_GAMES:
        context = get_game_context(team1, team2)
        
        print(f"\nApplying context for {team1} vs {team2}:")
        print(f"  Total: {context['total']}")
        print(f"  {team1} implied total: {context['team1_total']:.1f}")
        print(f"  {team2} implied total: {context['team2_total']:.1f}")
        
        # Adjust team1 players
        team1_mask = adjusted_df['team'] == team1
        if team1_mask.any():
            multiplier = context['team1_total'] / 22.5  # Scale from average
            adjusted_df.loc[team1_mask, 'Projection'] *= multiplier
            
        # Adjust team2 players  
        team2_mask = adjusted_df['team'] == team2
        if team2_mask.any():
            multiplier = context['team2_total'] / 22.5
            adjusted_df.loc[team2_mask, 'Projection'] *= multiplier
    
    # Round projections
    adjusted_df['Projection'] = adjusted_df['Projection'].round(2)
    
    return adjusted_df

def main():
    """Main pipeline function."""
    print("=== FOCUSED NFL PROJECTIONS PIPELINE ===")
    print(f"Target games: {TARGET_GAMES}")
    
    # Load data
    print("\n1. Loading data...")
    depth_df = load_depth_charts()
    props_df = load_player_props()
    team_mapping = create_team_name_mapping()
    
    print(f"   Loaded {len(depth_df)} depth chart entries")
    print(f"   Loaded {len(props_df)} player props")
    
    # Create offensive projections
    print("\n2. Creating offensive projections...")
    offensive_proj = create_offensive_projections(depth_df, props_df, team_mapping)
    print(f"   Created {len(offensive_proj)} offensive projections")
    
    # Create defensive projections
    print("\n3. Creating team defense projections...")
    defensive_proj = create_team_defense_projections_focused()
    print(f"   Created {len(defensive_proj)} team defense projections")
    
    # Combine projections
    print("\n4. Combining projections...")
    all_projections = pd.concat([offensive_proj, defensive_proj], ignore_index=True)
    
    # Apply game context adjustments
    print("\n5. Applying game context adjustments...")
    final_projections = apply_game_context_adjustments(all_projections)
    
    # Sort by projection descending
    final_projections = final_projections.sort_values('Projection', ascending=False)
    
    # Output in requested format (2 columns: Player, Projection)
    output_df = final_projections[['Player', 'Projection']].copy()
    
    print(f"\n=== FINAL PROJECTIONS ({len(output_df)} players) ===")
    print(output_df.to_string(index=False))
    
    # Save to file
    output_path = 'data/focused_projections.csv'
    output_df.to_csv(output_path, index=False)
    print(f"\nSaved projections to: {output_path}")
    
    return output_df

if __name__ == "__main__":
    projections = main()
